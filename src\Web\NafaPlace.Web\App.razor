@using Microsoft.AspNetCore.Components.Authorization

<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="container text-center py-5">
                            <h1 class="display-4">Accès Refusé</h1>
                            <p class="lead">Vous n'avez pas les autorisations nécessaires pour accéder à cette page.</p>
                            <a href="/" class="btn btn-primary">Retour à l'accueil</a>
                        </div>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Page non trouvée</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <div class="container text-center py-5">
                    <h1 class="display-1">404</h1>
                    <p class="lead">Désolé, la page que vous recherchez n'existe pas.</p>
                    <a href="/" class="btn btn-primary">
                        <i class="bi bi-house"></i> Retour à l'accueil
                    </a>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

@code {
    private class RedirectToLogin : ComponentBase
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        protected override void OnInitialized()
        {
            var returnUrl = Uri.EscapeDataString(NavigationManager.Uri);
            NavigationManager.NavigateTo($"/auth/login?returnUrl={returnUrl}", true);
        }
    }
}
